# Global LLM configuration
# 模型越好，生成的效果越好，建议使用最好的模型。
[llm]
api_type = "openai"  # 添加API类型，使用OpenAI兼容的API
model = "claude-3-7-sonnet-20250219"        # The LLM model to use, better use tool supported model
base_url = "https://api.anthropic.com/v1/"  # API endpoint URL
api_key = "YOUR_API_KEY"                    # Your API key
max_tokens = 8192                           # Maximum number of tokens in the response
temperature = 0.0                           # Controls randomness

# [llm] #AZURE OPENAI:
# api_type= 'azure'
# model = "YOUR_MODEL_NAME" #"gpt-4o-mini"
# base_url = "{YOUR_AZURE_ENDPOINT.rstrip('/')}/openai/deployments/{AZURE_DEPOLYMENT_ID}"
# api_key = "AZURE API KEY"
# max_tokens = 8096
# temperature = 0.0
# api_version="AZURE API VERSION" #"2024-08-01-preview"


# [llm]
# api_type = "ollama"
# model = "你的模型名称"  # 例如: "llama3.2", "qwen2.5", "deepseek-coder"
# base_url = "http://*************:8080/v1"  # 你的Ollama服务地址
# api_key = "ollama"  # 可以是任意值，Ollama会忽略但OpenAI SDK需要
# max_tokens = 4096
# temperature = 0.0

# Optional configuration for specific LLM models
# [llm.vision]
# model = "claude-3-7-sonnet-20250219"        # The vision model to use
# base_url = "https://api.anthropic.com/v1/"  # API endpoint URL for vision model
# api_key = "YOUR_API_KEY"                    # Your API key for vision model
# max_tokens = 8192                           # Maximum number of tokens in the response
# temperature = 0.0                           # Controls randomness for vision model

# [llm.vision] #OLLAMA VISION:
# api_type = 'ollama'
# model = "llama3.2-vision"
# base_url = "http://localhost:11434/v1"
# api_key = "ollama"
# max_tokens = 4096
# temperature = 0.0

# Optional configuration, Search settings.
[search]
# Search engine for agent to use. Default is "Google", can be set to "Baidu" or "DuckDuckGo" or "Bing"
# 对于国内用户，建议使用以下搜索引擎优先级：
# Baidu（百度）- 国内访问最稳定
# Bing（必应）- 国际化且国内可用
# Google - 作为备选（需要良好的国际网络）
# DuckDuckGo - 作为备选（需要良好的国际网络）
engine = "Bing"
